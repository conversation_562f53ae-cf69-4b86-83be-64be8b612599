# 缓存系统概览

## 🎯 系统简介

ChatToDesign 项目采用了简化的内存缓存架构，基于 Combine 实现 SWR (Stale-While-Revalidate) 模式，为应用提供高效的数据缓存和状态管理。

## ✨ 核心特性

### 🚀 性能优化
- **纯内存缓存**: 避免磁盘IO开销，提供最佳性能
- **请求去重**: 相同请求自动合并，减少网络调用
- **后台刷新**: 返回缓存数据的同时后台更新
- **智能失效**: 数据变更后自动失效相关缓存

### 🏗️ 架构优势
- **简化设计**: 专注内存缓存，降低复杂度
- **类型安全**: 完整的 Swift 类型支持
- **并发安全**: Actor 模式保证线程安全
- **易于集成**: 与现有架构无缝集成

## 📦 核心组件

### CacheManager
```swift
public actor CacheManager {
  // 默认共享实例
  public static let shared = CacheManager(configuration: .default)
  
  // 核心方法
  public func getCache<T: Codable>(for key: String, type: T.Type) async -> CacheItem<T>?
  public func setCache<T: Codable>(for key: String, value: T) async
  public func removeCache(for key: String) async
  public func clearAllCache() async
}
```

**职责**:
- 内存缓存存储和管理
- 自动内存警告处理
- 缓存大小和数量限制

### QueryManager
```swift
@MainActor
public class QueryManager: ObservableObject {
  // 默认共享实例
  public static let shared = QueryManager(cacheManager: CacheManager.shared)
  
  // 核心方法
  public func query<T: Codable>(...) -> AnyPublisher<QueryResult<T>, Never>
  public func mutate<T: Codable>(...) -> AnyPublisher<T, Error>
  public func invalidateQueries(keys: [String]) async
}
```

**职责**:
- SWR 逻辑实现
- 请求去重管理
- 缓存失效控制

### CacheFactory
```swift
public class CacheFactory {
  // 便捷访问方法
  public static var defaultCache: CacheManager
  public static var defaultQueryManager: QueryManager
  public static func createTestCache() -> CacheManager
}
```

**职责**:
- 统一的实例创建入口
- 测试环境支持
- 配置管理

## 🔧 配置选项

### CacheConfiguration
```swift
public struct CacheConfiguration {
  public let maxMemoryCacheSize: Int      // 内存缓存大小限制
  public let maxCacheAge: TimeInterval    // 缓存最大保存时间
  public let memoryCacheCountLimit: Int   // 缓存项数量限制
  public let namespace: String            // 缓存命名空间
}
```

### 预设配置
```swift
// 默认配置
CacheConfiguration.default = CacheConfiguration(
  maxMemoryCacheSize: 100 * 1024 * 1024,  // 100MB
  maxCacheAge: 24 * 60 * 60,              // 1天
  memoryCacheCountLimit: 2000,
  namespace: "default"
)
```

## 🎨 使用模式

### 1. 基本查询
```swift
queryManager
  .query(
    key: "cms_data",
    maxAge: 300,      // 5分钟后触发后台刷新
    staleTime: 3600,  // 1小时后数据完全过期
    networkCall: { try await self.apiService.fetchCMSData() }
  )
  .receive(on: DispatchQueue.main)
  .sink { result in
    switch result {
    case .cached(let data): // 新鲜缓存数据
    case .stale(let data):  // 过期缓存数据，正在后台刷新
    case .fresh(let data):  // 网络新数据
    case .error(let error): // 错误
    }
  }
  .store(in: &cancellables)
```

### 2. 数据变更
```swift
queryManager
  .mutate(
    key: "delete_asset_\(id)",
    invalidates: ["user_assets", "cms_data"],  // 失效相关缓存
    networkCall: { try await self.apiService.deleteAsset(id: id) }
  )
  .receive(on: DispatchQueue.main)
  .sink { result in /* 处理结果 */ }
  .store(in: &cancellables)
```

### 3. 自定义配置
```swift
// 创建短期缓存配置
let shortTermConfig = CacheConfiguration(
  maxMemoryCacheSize: 50 * 1024 * 1024,  // 50MB
  maxCacheAge: 60,                       // 1分钟
  memoryCacheCountLimit: 500,
  namespace: "short_term"
)

let customCache = CacheFactory.createCache(configuration: shortTermConfig)
let customQueryManager = await CacheFactory.createQueryManager(cacheManager: customCache)
```

## 📊 性能特点

### 内存使用
- **默认限制**: 100MB 或 2000 个条目
- **自动清理**: 内存警告时自动清空
- **智能管理**: LRU 策略自动淘汰旧数据

### 网络优化
- **请求去重**: 相同键的并发请求合并
- **后台刷新**: 不阻塞 UI 的数据更新
- **智能缓存**: 根据数据时效性决定是否发起请求

## 🧪 测试支持

```swift
class CacheTests: XCTestCase {
  var testQueryManager: QueryManager!
  
  override func setUp() {
    super.setUp()
    let testCache = CacheFactory.createTestCache()
    testQueryManager = QueryManager(cacheManager: testCache)
  }
  
  func testSWRBehavior() async {
    // 测试 stale-while-revalidate 行为
  }
}
```

## 🔍 调试工具

- `getCacheSize()` - 查看缓存大小
- `clearAllCache()` - 清理所有缓存
- `invalidateQueries()` - 手动失效缓存
- 详细的日志记录

## 📈 最佳实践

1. **合理设置缓存时间** - 根据数据更新频率调整
2. **使用有意义的缓存键** - 包含必要参数，避免冲突
3. **及时失效缓存** - 数据变更后失效相关缓存
4. **内存管理** - 使用 weak self 避免循环引用
5. **错误处理** - 处理所有可能的状态和错误

## 🎉 总结

简化后的缓存系统专注于内存缓存管理，提供了：

- **更好的性能** - 无磁盘IO开销，快速响应
- **更简单的架构** - 降低复杂度，易于维护
- **更强的可靠性** - 减少故障点，提高稳定性
- **更好的用户体验** - 即时数据显示，流畅交互

这种设计非常适合 iOS 应用的特点和需求。
