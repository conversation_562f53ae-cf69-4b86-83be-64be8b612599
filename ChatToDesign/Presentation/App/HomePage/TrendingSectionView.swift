//
//  TrendingSectionView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import Kingfisher
import SwiftUI

struct TrendingSectionView: View {
  let cmsCategories: [CMSCategory]
  let isLoading: Bool
  let error: Error?
  let onRetry: () -> Void
  let onItemTap: (CMSItem) -> Void

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Section header
      HStack {
        Text("Trending Styles")
          .font(.system(size: 20, weight: .semibold))
          .foregroundStyle(
            LinearGradient(
              gradient: Gradient(colors: [
                Color(red: 0.41, green: 0.65, blue: 0.81),
                Color(red: 0.41, green: 0.81, blue: 0.71),
              ]),
              startPoint: .leading,
              endPoint: .trailing
            )
          )

        Spacer()

        // Arrow icon - show only if there are more than 5 categories
        if cmsCategories.count > 5 {
          Button(action: {
            // TODO: Navigate to full styles gallery
            // This could show all categories in a grid view
          }) {
            Circle()
              .fill(Color.clear)
              .frame(width: 24, height: 24)
              .overlay(
                Image(systemName: "arrow.right")
                  .foregroundColor(.white)
                  .font(.system(size: 10, weight: .medium))
              )
          }
        }
      }
      .padding(.horizontal, 16)

      // Content based on loading state
      if isLoading {
        // Loading state
        ScrollView(.horizontal, showsIndicators: false) {
          HStack(spacing: 8) {
            ForEach(0..<3, id: \.self) { _ in
              TrendingCardView(title: "Loading...", imageURL: nil)
                .redacted(reason: .placeholder)
                .shimmer()
            }
          }
          .padding(.horizontal, 16)
        }
      } else if let error = error {
        // Error state
        VStack(spacing: 12) {
          Image(systemName: "exclamationmark.triangle")
            .font(.system(size: 24))
            .foregroundColor(.orange)

          Text("Failed to load trending styles")
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(.primary)

          // if let error = error {
          //   Text(error.localizedDescription)
          //     .font(.system(size: 12))
          //     .foregroundColor(.secondary)
          //     .multilineTextAlignment(.center)
          //     .lineLimit(2)
          // }

          Button("Retry") {
            onRetry()
          }
          .font(.system(size: 14, weight: .medium))
          .foregroundColor(.white)
          .padding(.horizontal, 16)
          .padding(.vertical, 8)
          .background(Color.blue)
          .cornerRadius(8)
        }
        .padding(.horizontal, 16)
        .frame(height: 140)
      } else if cmsCategories.isEmpty {
        // Empty state
        Text("No trending styles available")
          .font(.system(size: 14))
          .foregroundColor(.secondary)
          .padding(.horizontal, 16)
          .frame(height: 140)
      } else {
        // Success state with CMS data
        ScrollView(.horizontal, showsIndicators: false) {
          HStack(spacing: 8) {
            ForEach(cmsCategories.prefix(5), id: \.id) { category in
              if let firstItem = category.items.first {
                TrendingCardView(
                  title: firstItem.name,
                  imageURL: firstItem.outputUrl
                )
                .onTapGesture {
                  onItemTap(firstItem)
                }
              }
            }
          }
          .padding(.horizontal, 16)
        }
      }
    }
  }
}

struct TrendingCardView: View {
  let title: String
  let imageURL: String?

  var body: some View {
    ZStack(alignment: .bottom) {
      // Card background with image or placeholder
      if let imageURL = imageURL, let url = URL(string: imageURL) {
        KFImage(url)
          .placeholder {
            RoundedRectangle(cornerRadius: 8)
              .fill(
                LinearGradient(
                  gradient: Gradient(colors: [
                    Color(red: 0.3, green: 0.5, blue: 0.9),
                    Color(red: 0.5, green: 0.3, blue: 0.8),
                  ]),
                  startPoint: .topLeading,
                  endPoint: .bottomTrailing
                )
              )
              .frame(width: 140, height: 140)
          }
          .retry(maxCount: 3)
          .fade(duration: 0.25)
          .resizable()
          .aspectRatio(contentMode: .fill)
          .frame(width: 140, height: 140)
          .clipped()
      } else {
        RoundedRectangle(cornerRadius: 8)
          .fill(
            LinearGradient(
              gradient: Gradient(colors: [
                Color(red: 0.3, green: 0.5, blue: 0.9),
                Color(red: 0.5, green: 0.3, blue: 0.8),
              ]),
              startPoint: .topLeading,
              endPoint: .bottomTrailing
            )
          )
          .frame(width: 140, height: 140)
      }

      // Progressive blur overlay at bottom
      VStack {
        Spacer()

        // Bottom gradient overlay
        Rectangle()
          .fill(
            LinearGradient(
              gradient: Gradient(colors: [
                Color.clear,
                Color.black.opacity(0.8),
              ]),
              startPoint: .top,
              endPoint: .bottom
            )
          )
          .frame(height: 32)
          .clipShape(
            RoundedRectangle(cornerRadius: 8)
              .path(in: CGRect(x: 0, y: 8, width: 140, height: 24))
          )
      }

      // Title text
      HStack {
        Text(title)
          .font(.system(size: 13, weight: .semibold))
          .foregroundColor(.white)
          .padding(.leading, 8)
          .padding(.bottom, 8)

        Spacer()
      }
    }
    .clipShape(RoundedRectangle(cornerRadius: 8))
  }
}

#Preview {
  TrendingSectionView(
    cmsCategories: [
      CMSCategory(
        name: "Anime Artist",
        items: [
          CMSItem(
            id: "1",
            name: "Enchanted Anime",
            outputUrl:
              "https://a1d.ai/cdn-cgi/image/width=256,height=256,fit=scale-down,anim=0,f=auto/https://twitter-r2.a1d.ai/app-style/Anime-Artist/Anime-Artist_Enchanted-Anime.png",
            groupRank: 1,
            inGroupRank: 1,
            category: "Anime Artist",
            isDisabled: false,
            prompt: "Transform the image into a Studio Ghibli style",
            usecaseType: .promptOnly
          )
        ])
    ],
    isLoading: false,
    error: nil,
    onRetry: {},
    onItemTap: { item in
      print("Tapped item: \(item.name)")
    }
  )
}
