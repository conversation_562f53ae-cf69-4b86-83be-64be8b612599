import Kingfisher
import SwiftUI

struct CMSCategoriesView: View {
  let cmsCategories: [CMSCategory]
  let isLoading: Bool
  let error: Error?
  let onRetry: () -> Void
  let onItemTap: (CMSItem) -> Void

  // Filter out "Trending Styles" category since it's shown separately
  private var filteredCategories: [CMSCategory] {
    cmsCategories.filter { $0.name != "Trending Styles" }
  }

  var body: some View {
    VStack(alignment: .leading, spacing: 24) {
      if isLoading {
        // Loading state
        VStack(spacing: 16) {
          ForEach(0..<3, id: \.self) { _ in
            CategorySectionLoadingView()
          }
        }
      } else if let error = error {
        // Error state
        ErrorView(
          message: error.localizedDescription,
          onRetry: onRetry
        )
      } else {
        // Success state - show all categories except "Trending Styles"
        ForEach(filteredCategories, id: \.id) { category in
          CategorySectionView(
            category: category,
            onItemTap: onItemTap
          )
        }
      }
    }
  }
}

struct CategorySectionView: View {
  let category: CMSCategory
  let onItemTap: (CMSItem) -> Void

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Category title in white
      HStack {
        Text(category.name)
          .font(.system(size: 20, weight: .semibold))
          .foregroundColor(.primary)

        Spacer()
      }
      .padding(.horizontal, 16)

      // Horizontal scrolling items
      ScrollView(.horizontal, showsIndicators: false) {
        HStack(spacing: 8) {
          ForEach(category.items, id: \.id) { item in
            CategoryItemView(item: item) {
              onItemTap(item)
            }
          }
        }
        .padding(.horizontal, 16)
      }
    }
  }
}

struct CategoryItemView: View {
  let item: CMSItem
  let onTap: () -> Void

  var body: some View {
    Button(action: onTap) {
      ZStack(alignment: .bottom) {
        // Card background with image or placeholder
        if let url = URL(string: item.outputUrl) {
          KFImage(url)
            .placeholder {
              RoundedRectangle(cornerRadius: 8)
                .fill(
                  LinearGradient(
                    gradient: Gradient(colors: [
                      Color(red: 0.3, green: 0.5, blue: 0.9),
                      Color(red: 0.5, green: 0.3, blue: 0.8),
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                  )
                )
                .frame(width: 140, height: 140)
            }
            .retry(maxCount: 3)
            .fade(duration: 0.25)
            .resizable()
            .aspectRatio(contentMode: .fill)
            .frame(width: 140, height: 140)
            .clipped()
        } else {
          RoundedRectangle(cornerRadius: 8)
            .fill(
              LinearGradient(
                gradient: Gradient(colors: [
                  Color(red: 0.3, green: 0.5, blue: 0.9),
                  Color(red: 0.5, green: 0.3, blue: 0.8),
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
              )
            )
            .frame(width: 140, height: 140)
        }

        // Progressive blur overlay at bottom
        VStack {
          Spacer()

          // Bottom gradient overlay
          Rectangle()
            .fill(
              LinearGradient(
                gradient: Gradient(colors: [
                  Color.clear,
                  Color.black.opacity(0.8),
                ]),
                startPoint: .top,
                endPoint: .bottom
              )
            )
            .frame(height: 32)
            .clipShape(
              RoundedRectangle(cornerRadius: 8)
                .path(in: CGRect(x: 0, y: 8, width: 140, height: 24))
            )
        }

        // Title text
        HStack {
          Text(item.name)
            .font(.system(size: 13, weight: .semibold))
            .foregroundColor(.white)
            .padding(.leading, 8)
            .padding(.bottom, 8)

          Spacer()
        }
      }
      .clipShape(RoundedRectangle(cornerRadius: 8))
    }
    .buttonStyle(PlainButtonStyle())
  }
}

struct CategorySectionLoadingView: View {
  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // Title placeholder
      RoundedRectangle(cornerRadius: 4)
        .fill(Color.gray.opacity(0.3))
        .frame(width: 150, height: 24)

      // Items placeholder
      ScrollView(.horizontal, showsIndicators: false) {
        HStack(spacing: 8) {
          ForEach(0..<5, id: \.self) { _ in
            RoundedRectangle(cornerRadius: 8)
              .fill(Color.gray.opacity(0.3))
              .frame(width: 140, height: 140)
          }
        }
        .padding(.horizontal, 16)
      }
    }
  }
}

struct ErrorView: View {
  let message: String
  let onRetry: () -> Void

  var body: some View {
    VStack(spacing: 16) {
      Image(systemName: "exclamationmark.triangle")
        .font(.largeTitle)
        .foregroundColor(.orange)

      Text("Failed to load content")
        .font(.headline)
        .foregroundColor(.primary)

      Text(message)
        .font(.caption)
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)

      Button("Retry") {
        onRetry()
      }
      .buttonStyle(.borderedProminent)
    }
    .padding()
    .background(Color.gray.opacity(0.1))
    .cornerRadius(12)
  }
}

#Preview {
  CMSCategoriesView(
    cmsCategories: [
      CMSCategory(
        //        id: "1",
        name: "Trending Styles",
        items: [
          CMSItem(
            id: "1",
            name: "Enchanted Anime",
            outputUrl: "https://example.com/image1.jpg",
            groupRank: 1,
            inGroupRank: 1,
            category: "Trending Styles",
            isDisabled: false,
            prompt: "anime style",
            usecaseType: .promptWithImage
          ),
          CMSItem(
            id: "2",
            name: "RoleMode6",
            outputUrl: "https://example.com/image2.jpg",
            groupRank: 1,
            inGroupRank: 2,
            category: "Trending Styles",
            isDisabled: false,
            prompt: "professional style",
            usecaseType: .imageOnly
          ),
        ]
      ),
      CMSCategory(
        //        id: "2",
        name: "Animation",
        items: [
          CMSItem(
            id: "3",
            name: "Disney",
            outputUrl: "https://example.com/image3.jpg",
            groupRank: 2,
            inGroupRank: 1,
            category: "Animation",
            isDisabled: false,
            prompt: "disney style",
            usecaseType: .multiImage
          )
        ]
      ),
    ],
    isLoading: false,
    error: nil,
    onRetry: {},
    onItemTap: { _ in }
  )
}
