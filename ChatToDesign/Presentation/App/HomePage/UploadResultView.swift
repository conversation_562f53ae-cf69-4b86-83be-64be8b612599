//
//  UploadResultView.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/5.
//

import SwiftUI

// MARK: - Image Source Enum
enum ImageSource {
  case uploaded(UIImage)
  case asset(AssetResponse)
}

struct UploadResultView: View {
  let imageSource: ImageSource
  let cmsItem: CMSItem
  let onDismiss: () -> Void

  @Environment(\.colorScheme) private var colorScheme
  @StateObject private var designGenerationHandler: DesignGenerationHandler
  @State private var loadedImage: UIImage?
  @State private var isLoadingImage: Bool = false
  @State private var imageLoadError: String?

  // MARK: - Initialization
  init(imageSource: ImageSource, cmsItem: CMSItem, onDismiss: @escaping () -> Void) {
    self.imageSource = imageSource
    self.cmsItem = cmsItem
    self.onDismiss = onDismiss

    self._designGenerationHandler = StateObject(
      wrappedValue: DesignGenerationHandler()
    )
  }

  // Convenience initializer for backward compatibility
  init(selectedImage: UIImage, cmsItem: CMSItem, onDismiss: @escaping () -> Void) {
    self.init(imageSource: .uploaded(selectedImage), cmsItem: cmsItem, onDismiss: onDismiss)
  }

  var body: some View {
    ZStack {
      // Background
      Color.black
        .ignoresSafeArea()

      VStack(spacing: 0) {
        // Top navigation
        topNavigationBar

        // Main content area
        mainContentArea

        // Bottom content area
        bottomContentArea
      }
    }
    .alert("Generation Error", isPresented: .constant(designGenerationHandler.errorMessage != nil))
    {
      Button("OK") {
        // Clear error and optionally dismiss
        designGenerationHandler.cancelGeneration()
      }
    } message: {
      if let errorMessage = designGenerationHandler.errorMessage {
        Text(errorMessage)
      }
    }
    .alert("Image Load Error", isPresented: .constant(imageLoadError != nil)) {
      Button("OK") {
        imageLoadError = nil
        onDismiss()
      }
    } message: {
      if let error = imageLoadError {
        Text(error)
      }
    }
    .onChange(of: designGenerationHandler.taskCreatedSuccessfully) { taskCreated in
      if taskCreated {
        // Auto dismiss when task is created successfully
        onDismiss()
      }
    }
    .onAppear {
      loadImageIfNeeded()
    }
  }

  // MARK: - Top Navigation Bar

  private var topNavigationBar: some View {
    HStack {
      // Back button
      Button(action: onDismiss) {
        Image(systemName: "chevron.left")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.white)
      }

      Spacer()

      // Top indicator line
      Rectangle()
        .fill(Color.white)
        .frame(width: 48, height: 4)
        .cornerRadius(2)

      Spacer()

      // Close button
      Button(action: onDismiss) {
        Image(systemName: "xmark")
          .font(.system(size: 18, weight: .medium))
          .foregroundColor(.white)
      }
    }
    .padding(.horizontal, 20)
    .padding(.top, 16)
    .frame(height: 60)
  }

  // MARK: - Main Content Area

  private var mainContentArea: some View {
    VStack(spacing: 40) {
      // Title and subtitle
      VStack(spacing: 8) {
        Text("Photo ready to go")
          .font(.system(size: 24, weight: .semibold))
          .foregroundColor(.white)
          .multilineTextAlignment(.center)

        Text(cmsItem.name)
          .font(.system(size: 16, weight: .regular))
          .foregroundColor(.gray)
          .multilineTextAlignment(.center)
      }
      .padding(.top, 40)

      // Main uploaded image with controls
      ZStack {
        // Selected image
        Group {
          if isLoadingImage {
            // Loading state
            RoundedRectangle(cornerRadius: 20)
              .fill(Color.gray.opacity(0.3))
              .frame(width: 280, height: 420)
              .overlay(
                ProgressView()
                  .progressViewStyle(CircularProgressViewStyle(tint: .white))
                  .scaleEffect(1.2)
              )
          } else if let displayImage = currentDisplayImage {
            // Display loaded image
            Image(uiImage: displayImage)
              .resizable()
              .aspectRatio(contentMode: .fill)
              .frame(width: 280, height: 420)
              .clipShape(RoundedRectangle(cornerRadius: 20))
          } else {
            // Error state
            RoundedRectangle(cornerRadius: 20)
              .fill(Color.gray.opacity(0.3))
              .frame(width: 280, height: 420)
              .overlay(
                VStack(spacing: 8) {
                  Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 24))
                    .foregroundColor(.white)
                  Text("Failed to load image")
                    .font(.system(size: 14))
                    .foregroundColor(.white)
                }
              )
          }
        }

        // Control buttons overlay
        // VStack {
        //   HStack {
        //     Spacer()

        //     // Control buttons
        //     HStack(spacing: 8) {
        //       // Share button
        //       Button(action: {}) {
        //         Circle()
        //           .fill(Color.black.opacity(0.6))
        //           .frame(width: 36, height: 36)
        //           .overlay(
        //             Image(systemName: "square.and.arrow.up")
        //               .font(.system(size: 14, weight: .medium))
        //               .foregroundColor(.white)
        //           )
        //       }

        //       // Close button
        //       Button(action: onDismiss) {
        //         Circle()
        //           .fill(Color.black.opacity(0.6))
        //           .frame(width: 36, height: 36)
        //           .overlay(
        //             Image(systemName: "xmark")
        //               .font(.system(size: 14, weight: .medium))
        //               .foregroundColor(.white)
        //           )
        //       }
        //     }
        //     .padding(.trailing, 12)
        //   }
        //   .padding(.top, 12)

        //   Spacer()
        // }
      }

      Spacer()
    }
  }

  // MARK: - Bottom Content Area

  private var bottomContentArea: some View {
    VStack(spacing: 0) {
      // Generate button
      Button(action: {
        handleGenerateAction()
      }) {
        HStack {
          if designGenerationHandler.isLoading {
            ProgressView()
              .progressViewStyle(
                CircularProgressViewStyle(tint: Color.black)
              )
              .scaleEffect(0.8)
            Text("Generating...")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.black)
          } else {
            Text("Generate")
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.black)
          }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 56)
        .background(Color.white)
        .cornerRadius(28)
      }
      .disabled(designGenerationHandler.isLoading || isLoadingImage || currentDisplayImage == nil)
      .padding(.horizontal, 32)
      .padding(.bottom, 40)
    }
  }

  // MARK: - Computed Properties

  private var currentDisplayImage: UIImage? {
    switch imageSource {
    case .uploaded(let image):
      return image
    case .asset(_):
      return loadedImage
    }
  }

  // MARK: - Private Methods

  private func loadImageIfNeeded() {
    switch imageSource {
    case .uploaded(_):
      // Image is already available, no need to load
      break
    case .asset(let asset):
      loadImageFromAsset(asset)
    }
  }

  private func loadImageFromAsset(_ asset: AssetResponse) {
    guard let url = URL(string: asset.url) else {
      imageLoadError = "Invalid image URL"
      return
    }

    isLoadingImage = true

    Task {
      do {
        let (data, _) = try await URLSession.shared.data(from: url)

        await MainActor.run {
          if let image = UIImage(data: data) {
            self.loadedImage = image
          } else {
            self.imageLoadError = "Failed to decode image"
          }
          self.isLoadingImage = false
        }
      } catch {
        await MainActor.run {
          self.imageLoadError = "Failed to load image: \(error.localizedDescription)"
          self.isLoadingImage = false
        }
      }
    }
  }

  private func handleGenerateAction() {
    switch imageSource {
    case .uploaded(let image):
      // For uploaded images, use the traditional flow with UIImage
      designGenerationHandler.startGeneration(
        prompt: cmsItem.prompt,
        referenceImages: [image],
        designId: cmsItem.id,
        chatId: nil
      )

    case .asset(let asset):
      // For existing assets, use the URL-based flow to avoid re-uploading
      Task {
        do {
          try await designGenerationHandler.startGenerationWithImageUrls(
            prompt: cmsItem.prompt,
            imageUrls: [asset.url],
            designId: cmsItem.id,
            chatId: nil
          )
        } catch {
          Logger.error("Failed to start generation with asset: \(error.localizedDescription)")
        }
      }
    }

    // Note: The view will automatically dismiss when taskCreatedSuccessfully becomes true
    // This is handled by the onChange modifier in the body
  }
}

#Preview {
  UploadResultView(
    imageSource: .uploaded(UIImage(systemName: "photo")!),
    cmsItem: CMSItem(
      id: "1",
      name: "Enchanted Anime",
      outputUrl: "https://example.com/image.jpg",
      groupRank: 1,
      inGroupRank: 1,
      category: "Anime Artist",
      isDisabled: false,
      prompt: "Transform the image into a Studio Ghibli style",
      usecaseType: .promptWithImage
    ),
    onDismiss: {}
  )
}
