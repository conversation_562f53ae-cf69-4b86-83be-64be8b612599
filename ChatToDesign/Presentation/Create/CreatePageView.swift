//
//  CreatePageView.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import Combine
import SwiftUI

// MARK: - Supporting Types

/// 标签页类型
public enum TabType: CaseIterable {
  case videoTemplate
  case imageTemplate

  var title: String {
    switch self {
    case .videoTemplate:
      return "Video Template"
    case .imageTemplate:
      return "Image Template"
    }
  }
}

/// Create页面主视图
/// 展示视频和图片创建选项，以及模板选择
public struct CreatePageView: View {

  // MARK: - Properties

  @StateObject private var viewModel = CreatePageViewModel()
  @Environment(\.colorScheme) private var colorScheme
  @State private var showCreateVideo = false
  @State private var selectedVideoEffect: VideoEffect?
  @State private var selectedTemplate: TemplateItem?  // 保留用于图片模板

  // MARK: - Body

  public var body: some View {
    ZStack {
      // 背景
      Color.black
        .ignoresSafeArea()

      ScrollView {
        VStack(spacing: 0) {
          // 导航栏
          navigationBar

          // 内容区域
          contentSection

          // 底部间距，为底部导航栏留出空间
          Spacer()
            .frame(height: 120)
        }
      }
    }
    .preferredColorScheme(.dark)
    .navigationBarHidden(true)
    .onAppear {
      // 启动视频性能监控
      VideoPerformanceMonitor.shared.startMonitoring()
    }
    .onDisappear {
      // 停止性能监控
      VideoPerformanceMonitor.shared.stopMonitoring()
    }
    .fullScreenCover(isPresented: $showCreateVideo) {
      CreateVideoPageView()
    }
    .fullScreenCover(item: $selectedVideoEffect) { effect in
      CreateVideoFromTemplatePage(
        templateId: effect.template ?? effect.id,
        demoUrl: effect.videoUrl,
        inputImageUrls: [],
        inputImageLimits: effect.imageCount,
        inputGuide: effect.inputInstruction
          ?? "Upload images to create your video using this template",
        demoPrompt: effect.prompt ?? "Create a video using \(effect.name) template"
      )
    }
    .fullScreenCover(item: $selectedTemplate) { template in
      CreateVideoFromTemplatePage(
        templateId: template.id,
        demoUrl: template.videoURL ?? template.coverImageURL ?? "",
        inputImageUrls: [],  // Empty for now, could be populated from template data
        inputImageLimits: 1,  // Default limit
        inputGuide: "Upload images to create your video using this template",
        demoPrompt: "Create a video using \(template.name) template"
      )
    }
  }

  // MARK: - Navigation Bar

  private var navigationBar: some View {
    HStack {
      // 左侧标题
      Text("Create")
        .font(.system(size: 20, weight: .semibold))
        .foregroundColor(.white)

      Spacer()

      // 右侧可以添加其他控件
    }
    .padding(.horizontal, 24)
    .padding(.top, 16)
    .padding(.bottom, 24)
  }

  // MARK: - Content Section

  private var contentSection: some View {
    VStack(spacing: 40) {
      // 创建选项卡片
      createOptionsSection

      // 模板区域
      templatesSection
    }
    .padding(.horizontal, 24)
  }

  // MARK: - Create Options Section

  private var createOptionsSection: some View {
    HStack(spacing: 16) {
      // 视频创建卡片
      CreateOptionCard(
        title: "Video",
        backgroundColor: Color.purple,
        hasPlayButton: true
      ) {
        showCreateVideo = true
      }

      // 图片创建卡片
      CreateOptionCard(
        title: "Image",
        backgroundColor: Color.blue,
        hasPlayButton: false
      ) {
        viewModel.selectImageCreation()
      }
    }
  }

  // MARK: - Templates Section

  private var templatesSection: some View {
    VStack(spacing: 24) {
      // 标签页
      tabSection

      // 模板网格
      templateGrids
    }
  }

  // MARK: - Tab Section

  private var tabSection: some View {
    HStack(spacing: 0) {
      // Video Template 标签（激活状态）
      CreatePageTabButton(
        title: "Video Template",
        isActive: viewModel.selectedTab == TabType.videoTemplate
      ) {
        viewModel.selectTab(TabType.videoTemplate)
      }

      // Marketing Flyer 标签
      CreatePageTabButton(
        title: "Marketing Flyer",
        isActive: viewModel.selectedTab == TabType.imageTemplate
      ) {
        viewModel.selectTab(TabType.imageTemplate)
      }

      Spacer()
    }
  }

  // MARK: - Template Grids

  private var templateGrids: some View {
    VStack(spacing: 32) {
      if viewModel.selectedTab == .videoTemplate {
        videoEffectGrids
      } else {
        imageTemplateGrids
      }
    }
  }

  // MARK: - Video Effect Grids

  private var videoEffectGrids: some View {
    Group {
      if viewModel.isLoading && viewModel.videoEffectCategories.isEmpty {
        loadingView
      } else if let error = viewModel.error, viewModel.videoEffectCategories.isEmpty {
        errorView(error)
      } else {
        VStack(spacing: 32) {
          // 展示所有视频效果分类
          ForEach(viewModel.videoEffectCategories, id: \.id) { category in
            if !category.effects.isEmpty {
              VideoEffectTemplateGroup(
                title: category.name,
                videoEffects: category.effects,
                onEffectTap: { effect in
                  selectedVideoEffect = effect
                }
              )
            }
          }
        }
      }
    }
  }

  // MARK: - Image Template Grids

  private var imageTemplateGrids: some View {
    VStack(spacing: 32) {
      // Interactive 图片模板组
      TemplateGroup(
        title: "Interactive",
        templates: viewModel.interactiveTemplates,
        onTemplateTap: { template in
          selectedTemplate = template
        }
      )

      // Funny 图片模板组
      TemplateGroup(
        title: "Funny",
        templates: viewModel.funnyTemplates,
        onTemplateTap: { template in
          selectedTemplate = template
        }
      )
    }
  }

  // MARK: - Loading and Error Views

  private var loadingView: some View {
    VStack(spacing: 16) {
      ProgressView()
        .scaleEffect(1.2)

      Text("Loading Templates...")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(.secondary)
    }
    .frame(maxWidth: .infinity)
    .frame(height: 200)
  }

  private func errorView(_ error: Error) -> some View {
    VStack(spacing: 16) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 32))
        .foregroundColor(.orange)

      Text("Failed to Load Templates")
        .font(.system(size: 16, weight: .semibold))
        .foregroundColor(.primary)

      Text(error.localizedDescription)
        .font(.system(size: 14))
        .foregroundColor(.secondary)
        .multilineTextAlignment(.center)
        .padding(.horizontal, 32)

      Button("Retry") {
        viewModel.refresh()
      }
      .font(.system(size: 14, weight: .medium))
      .foregroundColor(.white)
      .padding(.horizontal, 20)
      .padding(.vertical, 8)
      .background(Color.blue)
      .cornerRadius(8)
    }
    .frame(maxWidth: .infinity)
    .frame(height: 200)
  }
}

// MARK: - Create Option Card

struct CreateOptionCard: View {
  let title: String
  let backgroundColor: Color
  let hasPlayButton: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      ZStack {
        // 背景
        backgroundColor
          .frame(height: 80)
          .cornerRadius(16)

        HStack {
          // 标题
          VStack(alignment: .leading) {
            Text(title)
              .font(.system(size: 16, weight: .medium))
              .foregroundColor(.white)

            Spacer()
          }
          .padding(.leading, 20)
          .padding(.vertical, 12)

          Spacer()

          // 右侧图标区域
          if hasPlayButton {
            ZStack {
              // 背景图片占位
              RoundedRectangle(cornerRadius: 8)
                .fill(Color.black.opacity(0.2))
                .frame(width: 64, height: 64)

              // 播放按钮
              Image(systemName: "play.circle")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
            }
            .padding(.trailing, 12)
          } else {
            // 图片占位
            RoundedRectangle(cornerRadius: 8)
              .fill(Color.black.opacity(0.2))
              .frame(width: 64, height: 64)
              .padding(.trailing, 12)
          }
        }

        // 边框
        RoundedRectangle(cornerRadius: 16)
          .stroke(Color.white, lineWidth: 1)
      }
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Tab Button

struct CreatePageTabButton: View {
  let title: String
  let isActive: Bool
  let action: () -> Void

  var body: some View {
    Button(action: action) {
      Text(title)
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(isActive ? .white : .secondary)
        .padding(.horizontal, 12)
        .padding(.vertical, 4)
        .background(
          isActive ? Color.gray.opacity(0.3) : Color.clear
        )
        .cornerRadius(.infinity)
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Template Group

struct TemplateGroup: View {
  let title: String
  let templates: [TemplateItem]
  let onTemplateTap: (TemplateItem) -> Void

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // 组标题
      HStack {
        Text(title)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.primary)

        Spacer()
      }

      // 模板网格
      LazyVGrid(
        columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 2), spacing: 16
      ) {
        ForEach(templates) { template in
          TemplateCard(template: template) {
            onTemplateTap(template)
          }
        }
      }
    }
  }
}

// MARK: - Template Card

struct TemplateCard: View {
  let template: TemplateItem
  let onTap: () -> Void

  var body: some View {
    Button(action: onTap) {
      VStack(spacing: 0) {
        // 模板图片
        ZStack {
          RoundedRectangle(cornerRadius: 16)
            .fill(Color.gray.opacity(0.3))
            .aspectRatio(3 / 4, contentMode: .fit)

          // 占位图片
          Image(systemName: "photo")
            .font(.system(size: 24))
            .foregroundColor(.secondary)
        }

        // 模板信息
        VStack(spacing: 8) {
          HStack {
            Text(template.name)
              .font(.system(size: 14, weight: .semibold))
              .foregroundColor(.white)

            Spacer()
          }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 8)
        .background(
          Color.black.opacity(0.14)
            .blur(radius: 2)
        )
      }
      .cornerRadius(16)
      .overlay(
        RoundedRectangle(cornerRadius: 16)
          .stroke(Color.gray.opacity(0.3), lineWidth: 1)
      )
    }
    .buttonStyle(PlainButtonStyle())
  }
}

// MARK: - Video Effect Template Group

struct VideoEffectTemplateGroup: View {
  let title: String
  let videoEffects: [VideoEffect]
  let onEffectTap: (VideoEffect) -> Void

  var body: some View {
    VStack(alignment: .leading, spacing: 16) {
      // 组标题
      HStack {
        Text(title)
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.primary)

        Spacer()
      }

      // 使用虚拟化视频网格
      VirtualizedVideoGrid(
        videoEffects: videoEffects,
        onEffectTap: onEffectTap
      )
      .frame(height: 316) // 2行 x 150 + 间距
    }
  }
}

#Preview {
  CreatePageView()
}
