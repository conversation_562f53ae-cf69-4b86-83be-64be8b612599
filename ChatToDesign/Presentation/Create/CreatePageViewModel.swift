//
//  CreatePageViewModel.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/2.
//

import Combine
import SwiftUI

/// 模板项目 (用于图片模板)
public struct TemplateItem: Identifiable, Equatable {
  public let id: String
  public let name: String
  public let category: String
  public let isHot: Bool
  public let isNew: Bool
  public var coverImageURL: String?
  public var videoURL: String?

  public init(
    id: String,
    name: String,
    category: String,
    isHot: Bool = false,
    isNew: Bool = false,
    coverImageURL: String? = nil,
    videoURL: String? = nil
  ) {
    self.id = id
    self.name = name
    self.category = category
    self.isHot = isHot
    self.isNew = isNew
    self.coverImageURL = coverImageURL
    self.videoURL = videoURL
  }
}

/// Create页面的ViewModel
/// 管理创建页面的状态和业务逻辑
@MainActor
public class CreatePageViewModel: ObservableObject {

  // MARK: - Published Properties

  /// 当前选中的标签页
  @Published public var selectedTab: TabType = .videoTemplate

  /// 视频效果分类数据
  @Published public var videoEffectCategories: [VideoEffectCategory] = []

  /// 交互式模板列表 (保留用于图片模板)
  @Published public var interactiveTemplates: [TemplateItem] = []

  /// 有趣模板列表 (保留用于图片模板)
  @Published public var funnyTemplates: [TemplateItem] = []

  /// 加载状态
  @Published public var isLoading: Bool = false

  /// 错误信息
  @Published public var error: Error?

  // MARK: - Private Properties

  private let videoEffectService: VideoEffectService
  private var videoEffectsHook: SWRHook<[VideoEffect]>?
  private var cancellables = Set<AnyCancellable>()

  // MARK: - Initialization

  public init(
    videoEffectService: VideoEffectService = AppDependencyContainer.shared.videoEffectService
  ) {
    self.videoEffectService = videoEffectService
    setupVideoEffectsHook()
    setupImageTemplateMockData()  // 保留用于图片模板
  }

  // MARK: - Computed Properties

  /// 所有视频效果（按分类排序）
  public var allVideoEffects: [VideoEffect] {
    videoEffectCategories.flatMap { $0.effects }
  }

  // MARK: - Public Methods

  /// 选择视频创建
  public func selectVideoCreation() {
    // TODO: 实现视频创建逻辑
    print("Video creation selected")
  }

  /// 选择图片创建
  public func selectImageCreation() {
    // TODO: 实现图片创建逻辑
    print("Image creation selected")
  }

  /// 选择标签页
  /// - Parameter tab: 要选择的标签页类型
  public func selectTab(_ tab: TabType) {
    selectedTab = tab
    loadTemplatesForTab(tab)
  }

  /// 选择模板
  /// - Parameter template: 选中的模板
  public func selectTemplate(_ template: TemplateItem) {
    // TODO: 实现模板选择逻辑
    print("Template selected: \(template.name)")
  }

  /// 刷新数据
  public func refresh() {
    videoEffectsHook?.refresh()
  }

  // MARK: - Private Methods

  /// 设置 VideoEffects SWR Hook
  private func setupVideoEffectsHook() {
    videoEffectsHook = SWRHook.create(
      key: "create_page_video_effects",
      maxAge: 60 * 60,  // 1 hour cache
      staleTime: 24 * 60 * 60,  // 24 hours stale time
      networkCall: { [weak self] in
        guard let self = self else {
          throw VideoEffectServiceError.invalidData
        }
        return try await self.videoEffectService.fetchVideoEffects()
      },
      autoFetch: true
    )

    observeVideoEffectsHookChanges()
  }

  /// 观察 VideoEffects Hook 变化
  private func observeVideoEffectsHookChanges() {
    guard let hook = videoEffectsHook else { return }

    // Observe data changes
    hook.$data
      .receive(on: DispatchQueue.main)
      .sink { [weak self] videoEffects in
        if let effects = videoEffects {
          self?.processVideoEffectsData(effects)
        }
      }
      .store(in: &cancellables)

    // Observe loading state
    hook.$isLoading
      .receive(on: DispatchQueue.main)
      .sink { [weak self] isLoading in
        self?.isLoading = isLoading
      }
      .store(in: &cancellables)

    // Observe error state
    hook.$error
      .receive(on: DispatchQueue.main)
      .sink { [weak self] error in
        self?.error = error
      }
      .store(in: &cancellables)
  }

  /// 处理 VideoEffects 数据
  private func processVideoEffectsData(_ videoEffects: [VideoEffect]) {
    // Filter out pollo effects (if needed)
    let filteredEffects = videoEffects.filter { $0.provider != .polloAI }

    // Group effects by category
    let groupedEffects = Dictionary(grouping: filteredEffects) { $0.category }

    // Convert to VideoEffectCategory objects and sort by category name
    let categories = groupedEffects.map { (categoryName, effects) in
      VideoEffectCategory(name: categoryName, effects: effects)
    }.sorted { $0.name < $1.name }

    self.videoEffectCategories = categories
    Logger.info("Successfully processed \(categories.count) video effect categories for CreatePage")
  }

  /// 为指定标签页加载模板
  /// - Parameter tab: 标签页类型
  private func loadTemplatesForTab(_ tab: TabType) {
    // TODO: 从API加载真实数据
    // 这里暂时使用模拟数据
  }

  /// 设置图片模板模拟数据 (保留用于图片模板功能)
  private func setupImageTemplateMockData() {
    // 交互式图片模板
    interactiveTemplates = [
      TemplateItem(
        id: "img_1", name: "Interactive Image 1", category: "Interactive", isHot: false,
        isNew: true,
        coverImageURL: "https://picsum.photos/400/600?random=1"),
      TemplateItem(
        id: "img_2", name: "Interactive Image 2", category: "Interactive", isHot: true,
        isNew: false,
        coverImageURL: "https://picsum.photos/400/600?random=2"),
    ]

    // 有趣图片模板
    funnyTemplates = [
      TemplateItem(
        id: "img_7", name: "Funny Image 1", category: "Funny", isHot: false, isNew: false,
        coverImageURL: "https://picsum.photos/400/600?random=7"),
      TemplateItem(
        id: "img_8", name: "Funny Image 2", category: "Funny", isHot: true, isNew: false,
        coverImageURL: "https://picsum.photos/400/600?random=8"),
    ]
  }
}
